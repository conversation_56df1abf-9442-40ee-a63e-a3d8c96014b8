Configuration:
- Model: Advanced ResUNet (Pretraining)
- Dataset: /data/prusek/training_big
- Batch Size: 1 (Effective: 8 with gradient accumulation)
- Gradient Accumulation Steps: 8
- Gradient Clipping: 1.0
- Learning Rate: 5e-4
- Optimizer: adamw
- Output Directory: scripts/training/outputs/resunet_advanced_pretrained

Checking model capacity...
Total parameters: 78,210,661
Model size: 298.35 MB
Input shape: torch.Size([1, 3, 1024, 1024])
Output shape: torch.Size([1, 1, 1024, 1024])
Model loaded successfully!

Starting pretraining...
/home/<USER>/.local/lib/python3.9/site-packages/torch/cuda/__init__.py:789: UserWarning: Can't initialize NVML
  warnings.warn("Can't initialize NVML")
/home/<USER>/.local/lib/python3.9/site-packages/torch/cuda/__init__.py:789: UserWarning: Can't initialize NVML
  warnings.warn("Can't initialize NVML")
2025-09-04 11:37:24,389 - INFO - ============================================================
2025-09-04 11:37:24,389 - INFO - COMPLETE DATASET SUMMARY
2025-09-04 11:37:24,389 - INFO - ============================================================
2025-09-04 11:37:24,389 - INFO - Train samples: 28,778
2025-09-04 11:37:24,389 - INFO - Val samples:   2,539
2025-09-04 11:37:24,389 - INFO - Test samples:  653
2025-09-04 11:37:24,389 - INFO - Total samples: 31,970
============================================================
COMPLETE DATASET SUMMARY
============================================================
Train samples: 28,778
Val samples:   2,539
Test samples:  653
Total samples: 31,970
2025-09-04 11:37:24,389 - INFO - ============================================================
============================================================

TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539

TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539

TEST Dataset Summary:
  Total images found: 653
  Valid image-mask pairs: 653
  Images without masks: 0
  Final test dataset size: 653

============================================================
COMPLETE DATASET SUMMARY
============================================================
Dataset path: /data/prusek/training_big
Image size: 1024x1024
------------------------------------------------------------
Train samples: 28,778
Val samples:   2,539
Test samples:  653
------------------------------------------------------------
Total samples: 31,970
============================================================

[rank0]:[W904 11:37:25.528346803 ProcessGroupNCCL.cpp:1479] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())
W0904 11:37:26.178070 3544581 torch/multiprocessing/spawn.py:169] Terminating process 3544744 via signal SIGTERM
Traceback (most recent call last):
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1531, in <module>
    main()
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1526, in main
    mp.spawn(train, args=(world_size, args), nprocs=world_size, join=True)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 340, in spawn
    return start_processes(fn, args, nprocs, join, daemon, start_method="spawn")
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 296, in start_processes
    while not context.join():
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 215, in join
    raise ProcessRaisedException(msg, error_index, failed_process.pid)
torch.multiprocessing.spawn.ProcessRaisedException: 

-- Process 0 terminated with the following error:
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 90, in _wrap
    fn(i, *args)
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1268, in train
    model = DDP(model, device_ids=[rank], find_unused_parameters=True)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/parallel/distributed.py", line 835, in __init__
    _verify_param_shape_across_processes(self.process_group, parameters)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/distributed/utils.py", line 282, in _verify_param_shape_across_processes
    return dist._verify_params_across_processes(process_group, tensors, logger)
torch.distributed.DistBackendError: NCCL error in: /pytorch/torch/csrc/distributed/c10d/NCCLUtils.cpp:77, unhandled system error (run with NCCL_DEBUG=INFO for details), NCCL version 2.26.2
ncclSystemError: System call (e.g. socket, malloc) or external library call failed or device error. 
Last error:
nvmlInit_v2() failed: Driver/library version mismatch


Pretraining failed!
