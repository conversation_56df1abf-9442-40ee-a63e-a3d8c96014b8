#!/usr/bin/env python3
"""
Merge DTS and SpheroSeg test datasets into a single combined dataset.
<PERSON>les filename conflicts by prefixing files with source dataset name.
"""

import os
import shutil
from pathlib import Path
from tqdm import tqdm
import argparse

def merge_datasets(dts_path, spheroseg_path, output_path, dry_run=False):
    """
    Merge two test datasets into a single combined dataset.
    
    Args:
        dts_path: Path to DTS dataset
        spheroseg_path: Path to SpheroSeg dataset  
        output_path: Path for merged dataset output
        dry_run: If True, only show what would be done without copying files
    """
    dts_path = Path(dts_path)
    spheroseg_path = Path(spheroseg_path)
    output_path = Path(output_path)
    
    print(f"Merging datasets:")
    print(f"  DTS: {dts_path}")
    print(f"  SpheroSeg: {spheroseg_path}")
    print(f"  Output: {output_path}")
    print(f"  Dry run: {dry_run}")
    print("="*60)
    
    # Verify input datasets exist
    for dataset_path, name in [(dts_path, "DTS"), (spheroseg_path, "SpheroSeg")]:
        if not dataset_path.exists():
            raise FileNotFoundError(f"{name} dataset not found at {dataset_path}")
        
        images_dir = dataset_path / "images"
        masks_dir = dataset_path / "masks"
        
        if not images_dir.exists() or not masks_dir.exists():
            raise FileNotFoundError(f"{name} dataset missing images or masks directory")
    
    # Create output directories
    if not dry_run:
        output_images = output_path / "images"
        output_masks = output_path / "masks"
        output_images.mkdir(parents=True, exist_ok=True)
        output_masks.mkdir(parents=True, exist_ok=True)
    
    # Track statistics
    total_copied = 0
    conflicts_resolved = 0
    
    # Process each dataset
    datasets = [
        ("DTS", dts_path, "dts_"),
        ("SpheroSeg", spheroseg_path, "spheroseg_")
    ]
    
    for dataset_name, dataset_path, prefix in datasets:
        print(f"\nProcessing {dataset_name} dataset...")
        
        images_dir = dataset_path / "images"
        masks_dir = dataset_path / "masks"
        
        # Get all image files
        image_files = []
        for ext in ['*.png', '*.jpg', '*.jpeg', '*.tiff', '*.tif', '*.bmp']:
            image_files.extend(images_dir.glob(ext))
        
        print(f"Found {len(image_files)} images in {dataset_name}")
        
        # Process each image-mask pair
        for img_file in tqdm(image_files, desc=f"Copying {dataset_name}"):
            # Find corresponding mask file
            img_stem = img_file.stem
            mask_file = None
            
            # Try different mask extensions
            for ext in ['.png', '.jpg', '.jpeg', '.tiff', '.tif', '.bmp']:
                potential_mask = masks_dir / (img_stem + ext)
                if potential_mask.exists():
                    mask_file = potential_mask
                    break
            
            if mask_file is None:
                print(f"Warning: No mask found for {img_file.name}")
                continue
            
            # Determine output filenames
            img_output_name = img_file.name
            mask_output_name = mask_file.name
            
            # Check for conflicts and add prefix if needed
            if not dry_run:
                img_output_path = output_path / "images" / img_output_name
                mask_output_path = output_path / "masks" / mask_output_name
                
                if img_output_path.exists() or mask_output_path.exists():
                    # Add prefix to resolve conflict
                    img_output_name = prefix + img_file.name
                    mask_output_name = prefix + mask_file.name
                    img_output_path = output_path / "images" / img_output_name
                    mask_output_path = output_path / "masks" / mask_output_name
                    conflicts_resolved += 1
                
                # Copy files
                shutil.copy2(img_file, img_output_path)
                shutil.copy2(mask_file, mask_output_path)
            else:
                # Dry run - just check for conflicts
                if (output_path / "images" / img_output_name).exists() if output_path.exists() else False:
                    print(f"  Conflict would be resolved: {img_file.name} -> {prefix + img_file.name}")
                    conflicts_resolved += 1
            
            total_copied += 1
    
    # Print summary
    print(f"\n{'='*60}")
    print("MERGE SUMMARY")
    print("="*60)
    print(f"Total image-mask pairs processed: {total_copied}")
    print(f"Filename conflicts resolved: {conflicts_resolved}")
    
    if not dry_run:
        # Verify output
        output_images = output_path / "images"
        output_masks = output_path / "masks"
        
        final_images = len(list(output_images.glob("*")))
        final_masks = len(list(output_masks.glob("*")))
        
        print(f"Final merged dataset:")
        print(f"  Images: {final_images}")
        print(f"  Masks: {final_masks}")
        print(f"  Location: {output_path}")
        
        if final_images != final_masks:
            print("WARNING: Mismatch between number of images and masks!")
    else:
        print("Dry run completed - no files were copied")

def main():
    parser = argparse.ArgumentParser(description='Merge DTS and SpheroSeg test datasets')
    parser.add_argument('--dts-path', 
                       default='/home/<USER>/SpheroSeg/NN/diplomka/TEST_DATA/DTS',
                       help='Path to DTS dataset')
    parser.add_argument('--spheroseg-path',
                       default='/home/<USER>/SpheroSeg/NN/diplomka/TEST_DATA/SpheroSeg', 
                       help='Path to SpheroSeg dataset')
    parser.add_argument('--output-path',
                       default='/home/<USER>/SpheroSeg/NN/diplomka/TEST_DATA/Merged',
                       help='Output path for merged dataset')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be done without actually copying files')
    
    args = parser.parse_args()
    
    merge_datasets(args.dts_path, args.spheroseg_path, args.output_path, args.dry_run)

if __name__ == '__main__':
    main()
